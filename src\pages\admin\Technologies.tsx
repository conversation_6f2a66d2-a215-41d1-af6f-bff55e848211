import { useState, useMemo } from "react";
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Plus, Edit, Trash2, Upload, Code, Database, Smartphone, Globe, Settings, Star } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  Technology,
  TechnologyCategory,
  CreateTechnology,
  UpdateTechnology,
  CreateTechnologyCategory,
  UpdateTechnologyCategory,
  PROFICIENCY_LEVELS,
  ProficiencyLevel,
  getProficiencyDisplayName,
  getCategoryDisplayName,
  getTechnologiesByCategory,
  getActiveCategories
} from "@/types/technology";

// Frontend-specific interface for category display with icons
interface CategoryDisplay extends TechnologyCategory {
  icon: any;
  color: string;
}

export default function Technologies() {
  const { toast } = useToast();
  const [selectedCategory, setSelectedCategory] = useState<number>(1);
  const [isAddingTech, setIsAddingTech] = useState(false);
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [editingTech, setEditingTech] = useState<Technology | null>(null);

  // IMMEDIATE IMPROVEMENT: Add client-side pagination to limit DOM elements
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(9); // 9 cards per page (3x3 grid)

  // TODO: Replace with API call when backend is ready
  // TODO: Implement server-side pagination and filtering
  // TODO: Add loading states and error handling
  const [categories] = useState<CategoryDisplay[]>([
    {
      id: 1,
      name: "frontend",
      display_name: "Frontend Development",
      description: "UI/UX Technologies and frameworks",
      is_active: true,
      display_order: 1,
      created_at: "2024-01-01T00:00:00Z",
      icon: Globe,
      color: "blue"
    },
    {
      id: 2,
      name: "backend",
      display_name: "Backend Development",
      description: "Server-side technologies and databases",
      is_active: true,
      display_order: 2,
      created_at: "2024-01-01T00:00:00Z",
      icon: Database,
      color: "green"
    },
    {
      id: 3,
      name: "mobile",
      display_name: "Mobile Development",
      description: "Mobile app development frameworks",
      is_active: true,
      display_order: 3,
      created_at: "2024-01-01T00:00:00Z",
      icon: Smartphone,
      color: "purple"
    },
    {
      id: 4,
      name: "devops",
      display_name: "DevOps & Infrastructure",
      description: "Infrastructure, deployment and monitoring tools",
      is_active: true,
      display_order: 4,
      created_at: "2024-01-01T00:00:00Z",
      icon: Settings,
      color: "orange"
    },
  ]);

  const [technologies, setTechnologies] = useState<Technology[]>([
    {
      id: 1,
      name: "React",
      description: "Modern JavaScript library for building user interfaces",
      category_id: 1,
      proficiency_level: "expert",
      years_experience: 5,
      logo_url: null,
      is_featured: true,
      is_active: true,
      display_order: 1,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z"
    },
    {
      id: 2,
      name: "TypeScript",
      description: "Typed superset of JavaScript",
      category_id: 1,
      proficiency_level: "expert",
      years_experience: 4,
      logo_url: null,
      is_featured: true,
      is_active: true,
      display_order: 2,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z"
    },
    {
      id: 3,
      name: "Node.js",
      description: "JavaScript runtime for server-side development",
      category_id: 2,
      proficiency_level: "advanced",
      years_experience: 5,
      logo_url: null,
      is_featured: true,
      is_active: true,
      display_order: 1,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z"
    },
    {
      id: 4,
      name: "PostgreSQL",
      description: "Advanced open-source relational database",
      category_id: 2,
      proficiency_level: "advanced",
      years_experience: 6,
      logo_url: null,
      is_featured: false,
      is_active: true,
      display_order: 2,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z"
    },
    {
      id: 5,
      name: "React Native",
      description: "Mobile app development framework",
      category_id: 3,
      proficiency_level: "intermediate",
      years_experience: 3,
      logo_url: null,
      is_featured: false,
      is_active: true,
      display_order: 1,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z"
    },
    {
      id: 6,
      name: "Docker",
      description: "Containerization platform",
      category_id: 4,
      proficiency_level: "advanced",
      years_experience: 4,
      logo_url: null,
      is_featured: true,
      is_active: true,
      display_order: 1,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z"
    },
  ]);

  // IMMEDIATE IMPROVEMENT: Use useMemo for performance optimization
  const filteredTechnologies = useMemo(() => {
    return getTechnologiesByCategory(technologies, selectedCategory);
  }, [technologies, selectedCategory]);

  // IMMEDIATE IMPROVEMENT: Add client-side pagination to reduce DOM elements
  const paginatedTechnologies = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredTechnologies.slice(startIndex, endIndex);
  }, [filteredTechnologies, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredTechnologies.length / pageSize);

  // Reset to first page when category changes
  const handleCategoryChange = (categoryId: number) => {
    setSelectedCategory(categoryId);
    setCurrentPage(1);
  };

  const getProficiencyColor = (level: string) => {
    switch (level) {
      case "expert": return "text-green-600 bg-green-50";
      case "advanced": return "text-blue-600 bg-blue-50";
      case "intermediate": return "text-yellow-600 bg-yellow-50";
      case "beginner": return "text-gray-600 bg-gray-50";
      default: return "text-gray-600 bg-gray-50";
    }
  };

  const handleSaveTechnology = (tech: Partial<CreateTechnology | UpdateTechnology>) => {
    // TODO: Replace with API call when backend is ready
    // TODO: Add proper error handling and loading states
    // TODO: Implement optimistic updates for better UX
    if (editingTech) {
      // TODO: Use updateData for API call when backend is ready
      setTechnologies(prev => prev.map(t =>
        t.id === editingTech.id
          ? { ...t, ...tech, updated_at: new Date().toISOString() }
          : t
      ));
      toast({ title: "Technology Updated", description: "Technology has been successfully updated." });
    } else {
      const newTech: Technology = {
        id: Date.now(), // TODO: Remove when using real API
        name: tech.name || "",
        description: tech.description || null,
        category_id: selectedCategory,
        proficiency_level: (tech as CreateTechnology).proficiency_level || "beginner",
        years_experience: (tech as CreateTechnology).years_experience || 1,
        logo_url: (tech as CreateTechnology).logo_url || null,
        is_featured: (tech as CreateTechnology).is_featured || false,
        is_active: (tech as CreateTechnology).is_active ?? true,
        display_order: (tech as CreateTechnology).display_order || technologies.length + 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      setTechnologies(prev => [...prev, newTech]);
      toast({ title: "Technology Added", description: "New technology has been successfully added." });
    }
    setIsAddingTech(false);
    setEditingTech(null);
  };

  const deleteTechnology = (id: number) => {
    // TODO: Replace with API call when backend is ready
    // TODO: Add confirmation dialog for better UX
    // TODO: Add proper error handling
    setTechnologies(prev => prev.filter(t => t.id !== id));
    toast({ title: "Technology Deleted", description: "Technology has been removed." });
  };

  const TechnologyForm = ({ technology }: { technology?: Technology }) => {
    const [formData, setFormData] = useState({
      name: technology?.name || "",
      description: technology?.description || "",
      proficiency_level: technology?.proficiency_level || "beginner",
      years_experience: technology?.years_experience || 1,
      logo_url: technology?.logo_url || "",
      is_featured: technology?.is_featured || false,
      is_active: technology?.is_active ?? true,
    });

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="techName">Technology Name</Label>
            <Input
              id="techName"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., React"
            />
          </div>
          <div>
            <Label htmlFor="proficiency">Proficiency Level</Label>
            <Select value={formData.proficiency_level} onValueChange={(value: ProficiencyLevel) => setFormData(prev => ({ ...prev, proficiency_level: value }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(PROFICIENCY_LEVELS).map(([key, label]) => (
                  <SelectItem key={key} value={key}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Brief description of the technology"
            rows={3}
          />
        </div>

        <div>
          <Label htmlFor="experience">Years of Experience</Label>
          <Input
            id="experience"
            type="number"
            min="0"
            max="20"
            value={formData.years_experience}
            onChange={(e) => setFormData(prev => ({ ...prev, years_experience: parseInt(e.target.value) || 0 }))}
          />
        </div>

        <div>
          <Label>Technology Logo</Label>
          <div className="border-2 border-dashed border-border rounded-lg p-4 text-center">
            <Upload className="w-6 h-6 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm text-muted-foreground mb-2">Upload technology logo</p>
            {/* TODO: Implement file upload functionality when backend is ready */}
            <Button variant="outline" size="sm">Choose File</Button>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Switch
              id="featured"
              checked={formData.is_featured}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_featured: checked }))}
            />
            <Label htmlFor="featured">Featured Technology</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
            />
            <Label htmlFor="active">Active</Label>
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={() => { setIsAddingTech(false); setEditingTech(null); }}>
            Cancel
          </Button>
          <Button onClick={() => handleSaveTechnology(formData)}>
            {technology ? "Update" : "Add"} Technology
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Technologies Management</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setIsAddingCategory(true)}>
            Add Category
          </Button>
          <Button onClick={() => setIsAddingTech(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Technology
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <Card>
          <CardHeader>
            <CardTitle>Categories</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => handleCategoryChange(category.id)}
                  className={`w-full p-3 rounded-lg text-left transition-colors ${
                    selectedCategory === category.id
                      ? "bg-primary/10 text-primary border border-primary/20"
                      : "hover:bg-accent"
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <Icon className="w-5 h-5" />
                    <div>
                      <div className="font-medium">{category.display_name}</div>
                      <div className="text-xs text-muted-foreground">{category.description}</div>
                    </div>
                  </div>
                </button>
              );
            })}
          </CardContent>
        </Card>

        {/* Technologies Grid */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {categories.find(cat => cat.id === selectedCategory)?.display_name} Technologies
                <Badge variant="secondary">
                  {filteredTechnologies.length} technologies
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* TODO: Add loading skeleton when implementing API calls */}
              {/* TODO: Consider virtual scrolling for very large technology lists */}
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                {paginatedTechnologies.map((tech) => (
                  <Card key={tech.id} className={`relative ${!tech.is_active ? "opacity-60" : ""}`}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-accent rounded-lg flex items-center justify-center">
                            <Code className="w-5 h-5" />
                          </div>
                          <div>
                            <h3 className="font-semibold">{tech.name}</h3>
                            {tech.is_featured && (
                              <Star className="w-4 h-4 text-yellow-500 fill-current" />
                            )}
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setEditingTech(tech)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => deleteTechnology(tech.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>

                      <p className="text-sm text-muted-foreground mb-3">{tech.description}</p>

                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Proficiency:</span>
                          <Badge className={getProficiencyColor(tech.proficiency_level)}>
                            {getProficiencyDisplayName(tech.proficiency_level as ProficiencyLevel)}
                          </Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Experience:</span>
                          <span className="text-sm font-medium">{tech.years_experience} years</span>
                        </div>
                      </div>

                      <div className="flex justify-between items-center mt-4 pt-3 border-t">
                        <div className="flex space-x-2">
                          {tech.is_featured && (
                            <Badge variant="secondary" className="text-xs">Featured</Badge>
                          )}
                          <Badge variant={tech.is_active ? "secondary" : "outline"} className="text-xs">
                            {tech.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* IMMEDIATE IMPROVEMENT: Client-side pagination to reduce DOM elements */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between px-2 py-4 mt-6">
                  <div className="text-sm text-muted-foreground">
                    Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, filteredTechnologies.length)} of {filteredTechnologies.length} technologies
                  </div>
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                          className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>

                      {/* Show page numbers */}
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum: number;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <PaginationItem key={pageNum}>
                            <PaginationLink
                              onClick={() => setCurrentPage(pageNum)}
                              isActive={currentPage === pageNum}
                              className="cursor-pointer"
                            >
                              {pageNum}
                            </PaginationLink>
                          </PaginationItem>
                        );
                      })}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                          className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Add Technology Dialog */}
      <Dialog open={isAddingTech} onOpenChange={setIsAddingTech}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Technology</DialogTitle>
          </DialogHeader>
          <TechnologyForm />
        </DialogContent>
      </Dialog>

      {/* Edit Technology Dialog */}
      <Dialog open={!!editingTech} onOpenChange={() => setEditingTech(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Technology</DialogTitle>
          </DialogHeader>
          {editingTech && <TechnologyForm technology={editingTech} />}
        </DialogContent>
      </Dialog>
    </div>
  );
}