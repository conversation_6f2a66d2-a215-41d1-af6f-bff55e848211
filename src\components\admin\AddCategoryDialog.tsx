import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { 
  TechnologyCategory, 
  CreateTechnologyCategory, 
  UpdateTechnologyCategory 
} from "@/types/technology";

interface AddCategoryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddCategory?: (category: CreateTechnologyCategory) => void;
  onUpdateCategory?: (category: UpdateTechnologyCategory) => void;
  category?: TechnologyCategory | null; // For edit mode
  mode?: 'add' | 'edit';
}

export function AddCategoryDialog({
  open,
  onOpenChange,
  onAddCategory,
  onUpdateCategory,
  category,
  mode = 'add'
}: AddCategoryDialogProps) {
  const [formData, setFormData] = useState<CreateTechnologyCategory>({
    name: "",
    display_name: "",
    description: "",
    is_active: true,
    display_order: 0,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Update form data when category changes (for edit mode)
  useEffect(() => {
    if (category && mode === 'edit') {
      setFormData({
        name: category.name,
        display_name: category.display_name,
        description: category.description || "",
        is_active: category.is_active,
        display_order: category.display_order,
      });
    } else {
      // Reset form for add mode
      setFormData({
        name: "",
        display_name: "",
        description: "",
        is_active: true,
        display_order: 0,
      });
    }
    setErrors({});
  }, [category, mode, open]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    } else if (!/^[a-z_]+$/.test(formData.name)) {
      newErrors.name = "Name must be lowercase letters and underscores only";
    }

    if (!formData.display_name.trim()) {
      newErrors.display_name = "Display name is required";
    }

    if (formData.display_order < 0) {
      newErrors.display_order = "Display order must be 0 or greater";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) return;

    if (mode === 'edit' && category && onUpdateCategory) {
      const updateData: UpdateTechnologyCategory = {
        id: category.id,
        ...formData
      };
      onUpdateCategory(updateData);
    } else if (mode === 'add' && onAddCategory) {
      onAddCategory(formData);
    }

    handleClose();
  };

  const handleClose = () => {
    setFormData({
      name: "",
      display_name: "",
      description: "",
      is_active: true,
      display_order: 0,
    });
    setErrors({});
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{mode === 'edit' ? 'Edit Category' : 'Add New Category'}</DialogTitle>
          <DialogDescription>
            {mode === 'edit'
              ? 'Update the technology category details below.'
              : 'Create a new technology category. Fill in the details below.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            {/* Name */}
            <div>
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., frontend"
                className={errors.name ? "border-destructive" : ""}
              />
              {errors.name && <p className="text-sm text-destructive mt-1">{errors.name}</p>}
            </div>

            {/* Display Name */}
            <div>
              <Label htmlFor="display_name">Display Name *</Label>
              <Input
                id="display_name"
                value={formData.display_name}
                onChange={(e) => setFormData(prev => ({ ...prev, display_name: e.target.value }))}
                placeholder="e.g., Frontend Development"
                className={errors.display_name ? "border-destructive" : ""}
              />
              {errors.display_name && <p className="text-sm text-destructive mt-1">{errors.display_name}</p>}
            </div>
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Description of what technologies belong in this category"
              rows={3}
            />
          </div>

          {/* Display Order */}
          <div>
            <Label htmlFor="display_order">Display Order</Label>
            <Input
              id="display_order"
              type="number"
              min="0"
              value={formData.display_order}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                display_order: parseInt(e.target.value) || 0 
              }))}
              placeholder="0"
              className={errors.display_order ? "border-destructive" : ""}
            />
            {errors.display_order && <p className="text-sm text-destructive mt-1">{errors.display_order}</p>}
          </div>

          {/* Active Status */}
          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
            />
            <Label htmlFor="is_active">Active Category</Label>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            {mode === 'edit' ? 'Update' : 'Add'} Category
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
